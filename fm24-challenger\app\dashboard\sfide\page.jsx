'use client';

import { useState, useEffect } from 'react';
import supabase from '@/lib/supabase.js';
import { useRouter } from 'next/navigation';

export default function SfidePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [challenges, setChallenges] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [currentChallenge, setCurrentChallenge] = useState(null);
  const [translations, setTranslations] = useState([]);
  const [languages] = useState(['it', 'en']); // Lingue supportate
    // Paginazione e filtro
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredChallenges, setFilteredChallenges] = useState([]);
  const [selectedCategoria, setSelectedCategoria] = useState('');
  const [selectedDifficolta, setSelectedDifficolta] = useState('');
  const [selectedTranslationStatus, setSelectedTranslationStatus] = useState('');
  const [categorie, setCategorie] = useState([]);
  const [difficolta, setDifficolta] = useState([]);
    useEffect(() => {
    // Verifica se l'utente è autenticato e carica le sfide
    const checkSessionAndLoadChallenges = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        router.push('/login');
        return;
      }
      
      await loadChallenges();
    };
    
    checkSessionAndLoadChallenges();
  }, [router]);
    // Effetto per estrarre categorie e difficoltà uniche dalle sfide
  useEffect(() => {
    if (challenges.length > 0) {
      // Estrai categorie uniche
      const uniqueCategorie = [...new Set(challenges.map(challenge => challenge.categoria))].sort();
      setCategorie(uniqueCategorie);
      
      // Estrai difficoltà uniche
      const uniqueDifficolta = [...new Set(challenges.map(challenge => challenge.difficolta))].sort();
      setDifficolta(uniqueDifficolta);
    }
  }, [challenges]);
  // Effetto per filtrare le sfide quando cambiano i dati o i filtri
  useEffect(() => {
    const filtered = challenges.filter(challenge => {
      // Filtra per termine di ricerca testuale
      const matchTextSearch = searchTerm === '' || 
        challenge.categoria.toLowerCase().includes(searchTerm.toLowerCase()) || 
        challenge.difficolta.toLowerCase().includes(searchTerm.toLowerCase()) ||
        challenge.translations?.some(t => t.text?.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // Filtra per categoria selezionata
      const matchCategoria = selectedCategoria === '' || challenge.categoria === selectedCategoria;
      
      // Filtra per difficoltà selezionata
      const matchDifficolta = selectedDifficolta === '' || challenge.difficolta === selectedDifficolta;
      
      // Filtra per stato traduzioni
      let matchTranslationStatus = true;
      if (selectedTranslationStatus === 'complete') {
        // Verifica che ci siano tutte le traduzioni per tutte le lingue supportate
        matchTranslationStatus = languages.every(lang => 
          challenge.translations?.some(t => t.lang === lang && t.text?.trim() !== '')
        );
      } else if (selectedTranslationStatus === 'incomplete') {
        // Verifica che manchi almeno una traduzione
        matchTranslationStatus = languages.some(lang => 
          !challenge.translations?.some(t => t.lang === lang && t.text?.trim() !== '')
        );
      }
      
      // Deve soddisfare tutti i filtri applicati
      return matchTextSearch && matchCategoria && matchDifficolta && matchTranslationStatus;
    });
    
    setFilteredChallenges(filtered);
    setCurrentPage(1); // Reset alla prima pagina quando cambiano i filtri
  }, [challenges, searchTerm, selectedCategoria, selectedDifficolta, selectedTranslationStatus, languages]);  const loadChallenges = async () => {
    try {
      // Resetta i filtri durante il caricamento
      setSearchTerm('');
      setSelectedCategoria('');
      setSelectedDifficolta('');
      setSelectedTranslationStatus('');
      
      // Carica le sfide dal database
      const { data: challengesData, error: challengesError } = await supabase
        .from('challenges')
        .select('*');
      
      if (challengesError) {
        console.error("Errore nel caricamento delle challenge:", challengesError.message);
        throw challengesError;
      }
      
      // Se non ci sono sfide, imposta l'array vuoto e termina
      if (!challengesData || challengesData.length === 0) {
        setChallenges([]);
        setLoading(false);
        return;
      }
        // Per ogni sfida, carica le traduzioni
      const challengesWithTranslations = await Promise.all(challengesData.map(async (challenge) => {
        try {
          const { data: translationData, error: translationError } = await supabase
            .from('translations')
            .select('*')
            .eq('challenge_id', challenge.id);
            
          if (translationError) {
            console.error(`Errore nel caricamento delle traduzioni per la challenge ${challenge.id}:`, translationError.message);
            return {
              ...challenge,
              translations: [] 
            };
          }
          
          return {
            ...challenge,
            translations: translationData || []
          };
        } catch (innerError) {
          console.error(`Errore inaspettato per la challenge ${challenge.id}:`, innerError);
          return {
            ...challenge,
            translations: []
          };
        }
      }));
      
      setChallenges(challengesWithTranslations);
    } catch (error) {
      console.error("Errore nel caricamento delle sfide:", error.message || JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };
    const handleCreate = async (data, translations) => {
    try {
      // Inserisci la nuova sfida
      const { data: newChallenge, error: challengeError } = await supabase
        .from('challenges')
        .insert([{
          categoria: data.categoria,
          difficolta: data.difficolta
        }])
        .select();
      
      if (challengeError) {
        console.error("Errore nella creazione della sfida:", challengeError.message);
        alert(`Errore: ${challengeError.message}`);
        return;
      }
      
      if (!newChallenge || newChallenge.length === 0) {
        console.error("Errore: nessuna sfida creata");
        alert("Errore: nessuna sfida creata");
        return;
      }
      
      // Inserisci le traduzioni
      const translationPromises = translations.map(t => 
        supabase.from('translations').insert([{
          challenge_id: newChallenge[0].id,
          lang: t.lang,
          text: t.text
        }])
      );
      
      const translationResults = await Promise.all(translationPromises);
      
      // Verifica se ci sono errori nelle traduzioni
      const translationErrors = translationResults.filter(result => result.error);
      if (translationErrors.length > 0) {
        console.error("Errori nell'inserimento delle traduzioni:", translationErrors);
        alert("Avviso: alcune traduzioni potrebbero non essere state salvate correttamente.");
      }
      
      setShowCreateModal(false);
      await loadChallenges();
    } catch (error) {
      console.error("Errore nella creazione della sfida:", error.message || JSON.stringify(error));
      alert(`Si è verificato un errore: ${error.message || "errore sconosciuto"}`);
    }
  };
    const handleUpdate = async (data, translations) => {
    try {
      // Aggiorna la sfida
      const { error: challengeError } = await supabase
        .from('challenges')
        .update({
          categoria: data.categoria,
          difficolta: data.difficolta
        })
        .eq('id', currentChallenge.id);
      
      if (challengeError) {
        console.error("Errore nell'aggiornamento della sfida:", challengeError.message);
        alert(`Errore: ${challengeError.message}`);
        return;
      }
      
      // Gestisci le traduzioni
      const translationErrors = [];
      
      for (const translation of translations) {
        try {
          if (translation.id) {
            // Aggiorna la traduzione esistente
            const { error } = await supabase
              .from('translations')
              .update({ text: translation.text })
              .eq('id', translation.id);
              
            if (error) {
              translationErrors.push(`Errore aggiornamento traduzione ${translation.lang}: ${error.message}`);
            }
          } else {
            // Crea una nuova traduzione
            const { error } = await supabase
              .from('translations')
              .insert([{
                challenge_id: currentChallenge.id,
                lang: translation.lang,
                text: translation.text
              }]);
              
            if (error) {
              translationErrors.push(`Errore creazione traduzione ${translation.lang}: ${error.message}`);
            }
          }
        } catch (translationError) {
          translationErrors.push(`Errore per traduzione ${translation.lang}: ${translationError.message}`);
        }
      }
      
      // Mostra eventuali errori di traduzione
      if (translationErrors.length > 0) {
        console.error("Errori nelle traduzioni:", translationErrors);
        alert(`Avviso: alcune traduzioni potrebbero non essere state salvate correttamente.`);
      }
      
      setShowEditModal(false);
      await loadChallenges();
    } catch (error) {
      console.error("Errore nell'aggiornamento della sfida:", error.message || JSON.stringify(error));
      alert(`Si è verificato un errore: ${error.message || "errore sconosciuto"}`);
    }
  };
    const handleDelete = async () => {
    try {
      // Elimina prima le traduzioni (assumendo che ci sia una foreign key constraint)
      const { error: translationsError } = await supabase
        .from('translations')
        .delete()
        .eq('challenge_id', currentChallenge.id);
      
      if (translationsError) {
        console.error("Errore nell'eliminazione delle traduzioni:", translationsError.message);
        alert(`Errore nell'eliminazione delle traduzioni: ${translationsError.message}`);
        return;
      }
        
      // Poi elimina la sfida
      const { error: challengeError } = await supabase
        .from('challenges')
        .delete()
        .eq('id', currentChallenge.id);
      
      if (challengeError) {
        console.error("Errore nell'eliminazione della sfida:", challengeError.message);
        alert(`Errore nell'eliminazione della sfida: ${challengeError.message}`);
        return;
      }
      
      setShowDeleteModal(false);
      await loadChallenges();
    } catch (error) {
      console.error("Errore nell'eliminazione della sfida:", error.message || JSON.stringify(error));
      alert(`Si è verificato un errore: ${error.message || "errore sconosciuto"}`);
    }
  };
  
  const openEditModal = async (challenge) => {
    setCurrentChallenge(challenge);
    setTranslations(challenge.translations);
    setShowEditModal(true);
  };
  
  const openDeleteModal = (challenge) => {
    setCurrentChallenge(challenge);
    setShowDeleteModal(true);
  };
  
  // Calcolo dei dati della paginazione
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const displayedChallenges = filteredChallenges.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredChallenges.length / itemsPerPage);
  
  // Funzioni per la paginazione
  const goToPage = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };
    const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };
  
  const handleCategoriaChange = (e) => {
    setSelectedCategoria(e.target.value);
  };
    const handleDifficoltaChange = (e) => {
    setSelectedDifficolta(e.target.value);
  };
  
  const handleTranslationStatusChange = (e) => {
    setSelectedTranslationStatus(e.target.value);
  };
  
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset alla prima pagina
  };
    const resetFilters = () => {
    setSearchTerm('');
    setSelectedCategoria('');
    setSelectedDifficolta('');
    setSelectedTranslationStatus('');
  };
    if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#1a202c]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );  }
    return (
    <div className="bg-[#1a202c] min-h-screen p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-white">Gestione Sfide</h1>
        <button 
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
          onClick={() => setShowCreateModal(true)}
        >
          Crea Sfida
        </button>
      </div>
        <div className="mb-6 flex flex-col gap-4">
        {/* Prima riga: ricerca testuale e reset filtri */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="w-full md:w-2/3">
            <input
              type="text"
              placeholder="Cerca sfide..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-full p-2 rounded bg-[#273040] text-white border border-gray-700 focus:outline-none focus:border-blue-500"
            />
          </div>
          <button
            onClick={resetFilters}
            className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded w-full md:w-auto"
          >
            Resetta filtri
          </button>
        </div>
        
        {/* Seconda riga: select per categoria e difficoltà */}        <div className="flex flex-col md:flex-row gap-4">
          <div className="w-full md:w-1/4">
            <label className="block text-gray-300 mb-1 text-sm">Categoria</label>
            <select
              value={selectedCategoria}
              onChange={handleCategoriaChange}
              className="w-full p-2 rounded bg-[#273040] text-white border border-gray-700 focus:outline-none focus:border-blue-500"
            >
              <option value="">Tutte le categorie</option>
              {categorie.map(categoria => (
                <option key={categoria} value={categoria}>
                  {categoria}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-1/4">
            <label className="block text-gray-300 mb-1 text-sm">Difficoltà</label>
            <select
              value={selectedDifficolta}
              onChange={handleDifficoltaChange}
              className="w-full p-2 rounded bg-[#273040] text-white border border-gray-700 focus:outline-none focus:border-blue-500"
            >
              <option value="">Tutte le difficoltà</option>
              {difficolta.map(diff => (
                <option key={diff} value={diff}>
                  {diff}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-1/4">
            <label className="block text-gray-300 mb-1 text-sm">Stato traduzioni</label>
            <select
              value={selectedTranslationStatus}
              onChange={handleTranslationStatusChange}
              className="w-full p-2 rounded bg-[#273040] text-white border border-gray-700 focus:outline-none focus:border-blue-500"
            >
              <option value="">Qualsiasi</option>
              <option value="complete">Complete</option>
              <option value="incomplete">Incomplete</option>
            </select>
          </div>
          
          <div className="w-full md:w-1/4">
            <label className="block text-gray-300 mb-1 text-sm">Elementi per pagina</label>
            <select
              value={itemsPerPage}
              onChange={handleItemsPerPageChange}
              className="w-full p-2 rounded bg-[#273040] text-white border border-gray-700 focus:outline-none focus:border-blue-500"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>
        
        {/* Mostra il conteggio dei risultati filtrati */}
        <div className="text-sm text-gray-400">
          {filteredChallenges.length} risultati trovati
          {(selectedCategoria || selectedDifficolta || searchTerm) && (
            <span> con i filtri applicati</span>
          )}
        </div>
      </div>
      
      <div className="bg-[#1e252e] rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">          <thead className="bg-[#273040]">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Categoria
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Difficoltà
              </th>              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Testo (IT/EN)
              </th><th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>          <tbody className="bg-[#1e252e] divide-y divide-gray-700">
            {displayedChallenges.length === 0 ? (
              <tr>
                <td colSpan="4" className="px-6 py-8 text-center text-gray-400">
                  Nessuna sfida trovata con i filtri selezionati
                </td>
              </tr>
            ) : (
              displayedChallenges.map((challenge) => (
                // Contenuto esistente per ogni riga
                <tr key={challenge.id} className="hover:bg-[#273040]">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-gray-200">{challenge.categoria}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      challenge.difficolta === 'Facile' 
                        ? 'bg-green-800 text-green-100' 
                        : challenge.difficolta === 'Media' 
                        ? 'bg-yellow-800 text-yellow-100'
                        : 'bg-red-800 text-red-100'
                    }`}>
                      {challenge.difficolta}
                    </span>
                  </td>                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="flex-grow">
                        <div className="text-gray-300 max-w-xs truncate">
                          {challenge.translations?.find(t => t.lang === 'it')?.text || (
                            <span className="text-red-400">IT: Non disponibile</span>
                          )}
                        </div>
                        {challenge.translations?.find(t => t.lang === 'en') ? (
                          <div className="text-gray-500 text-xs mt-1 max-w-xs truncate">
                            EN: {challenge.translations?.find(t => t.lang === 'en')?.text}
                          </div>
                        ) : (
                          <div className="text-red-400 text-xs mt-1">
                            EN: Non disponibile
                          </div>
                        )}
                      </div>
                      {(!challenge.translations?.find(t => t.lang === 'it') || !challenge.translations?.find(t => t.lang === 'en')) && (
                        <div className="ml-2" title="Traduzioni mancanti">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      className="text-blue-400 hover:text-blue-200 mr-3"
                      onClick={() => openEditModal(challenge)}
                    >
                      Modifica
                    </button>
                    <button 
                      className="text-red-400 hover:text-red-200"
                      onClick={() => openDeleteModal(challenge)}
                    >
                      Elimina
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Paginazione */}
      <div className="flex justify-between items-center mt-6 text-white">
        <div>
          Mostrando {indexOfFirstItem + 1} - {Math.min(indexOfLastItem, filteredChallenges.length)} di {filteredChallenges.length} sfide
        </div>
        
        <div className="flex space-x-2">
          <button 
            onClick={() => goToPage(1)} 
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-700 text-gray-400' : 'bg-[#273040] hover:bg-[#324055]'}`}
          >
            &laquo;
          </button>
          
          <button 
            onClick={() => goToPage(currentPage - 1)} 
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-gray-700 text-gray-400' : 'bg-[#273040] hover:bg-[#324055]'}`}
          >
            &lsaquo;
          </button>
          
          {/* Numeri di pagina */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              // Se ci sono 5 o meno pagine totali, mostra tutte le pagine
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              // Se siamo nelle prime 3 pagine, mostra pagine 1-5
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              // Se siamo nelle ultime 3 pagine, mostra le ultime 5 pagine
              pageNum = totalPages - 4 + i;
            } else {
              // Altrimenti mostra 2 pagine prima e 2 dopo la corrente
              pageNum = currentPage - 2 + i;
            }
            
            return (
              <button 
                key={pageNum}
                onClick={() => goToPage(pageNum)}
                className={`px-3 py-1 rounded ${pageNum === currentPage ? 'bg-blue-600' : 'bg-[#273040] hover:bg-[#324055]'}`}
              >
                {pageNum}
              </button>
            );
          })}
          
          <button 
            onClick={() => goToPage(currentPage + 1)} 
            disabled={currentPage === totalPages || totalPages === 0}
            className={`px-3 py-1 rounded ${currentPage === totalPages || totalPages === 0 ? 'bg-gray-700 text-gray-400' : 'bg-[#273040] hover:bg-[#324055]'}`}
          >
            &rsaquo;
          </button>
          
          <button 
            onClick={() => goToPage(totalPages)} 
            disabled={currentPage === totalPages || totalPages === 0}
            className={`px-3 py-1 rounded ${currentPage === totalPages || totalPages === 0 ? 'bg-gray-700 text-gray-400' : 'bg-[#273040] hover:bg-[#324055]'}`}
          >
            &raquo;
          </button>
        </div>
      </div>
      
      {/* Modal per la creazione di una sfida */}
      {showCreateModal && (
        <ChallengeFormModal
          title="Crea Nuova Sfida"
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreate}
          languages={languages}
        />
      )}
      
      {/* Modal per la modifica di una sfida */}
      {showEditModal && (
        <ChallengeFormModal
          title="Modifica Sfida"
          challenge={currentChallenge}
          translations={translations}
          onClose={() => setShowEditModal(false)}
          onSubmit={handleUpdate}
          languages={languages}
        />
      )}
      
      {/* Modal di conferma eliminazione */}
      {showDeleteModal && (
        <ConfirmDeleteModal
          onClose={() => setShowDeleteModal(false)}
          onConfirm={handleDelete}
        />
      )}
    </div>
  );
}

// Componente modal per la creazione/modifica di una sfida
function ChallengeFormModal({ title, challenge = null, translations = [], onClose, onSubmit, languages }) {
  const [formData, setFormData] = useState({
    categoria: challenge?.categoria || '',
    difficolta: challenge?.difficolta || 'easy'
  });
  
  const [formTranslations, setFormTranslations] = useState(
    languages.map(lang => {
      const existingTranslation = translations.find(t => t.lang === lang);
      return {
        id: existingTranslation?.id || null,
        lang,
        text: existingTranslation?.text || ''
      };
    })
  );
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleTranslationChange = (lang, value) => {
    setFormTranslations(prev => 
      prev.map(t => t.lang === lang ? { ...t, text: value } : t)
    );
  };
    const handleSubmit = (e) => {
    e.preventDefault();
    
    // Verifica che tutte le traduzioni abbiano un testo
    const missingTranslations = formTranslations.filter(t => t.text.trim() === '');
    if (missingTranslations.length > 0) {
      const missingLangs = missingTranslations.map(t => t.lang.toUpperCase()).join(', ');
      alert(`Per favore, inserisci il testo per tutte le lingue: ${missingLangs}`);
      return;
    }
    
    onSubmit(formData, formTranslations);
  };
    return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-[#1e252e] rounded-lg p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <h2 className="text-2xl font-bold mb-6 text-white">{title}</h2>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-4">            <label className="block text-gray-300 mb-2">Categoria</label>
            <input
              type="text"
              name="categoria"
              value={formData.categoria}
              onChange={handleChange}
              className="w-full p-2 border border-gray-600 rounded bg-[#273040] text-white"
              required
            />
          </div>
          
          <div className="mb-6">
            <label className="block text-gray-300 mb-2">Difficoltà</label>
            <select
              name="difficolta"
              value={formData.difficolta}
              onChange={handleChange}
              className="w-full p-2 border border-gray-600 rounded bg-[#273040] text-white"
            >
              <option value="very_easy">Molto facile</option>
              <option value="easy">Facile</option>
              <option value="medium">Media</option>
              <option value="hard">Difficile</option>
              <option value="very_hard">Molto difficile</option>
              <option value="matta">Follia Pura</option>
              <option value="meme">Meme</option>
            </select>
          </div>
            <div className="mb-6">            <h3 className="text-lg font-semibold mb-3 text-gray-200">Traduzioni</h3>
            
            {formTranslations.map((translation) => (
              <div key={translation.lang} className="mb-4">
                <label className="flex items-center justify-between text-gray-300 mb-2">
                  <span>
                    {translation.lang.toUpperCase()} 
                    {translation.lang === 'it' && (
                      <span className="ml-2 text-xs bg-blue-600 text-white px-2 py-0.5 rounded">Principale</span>
                    )}
                  </span>
                  <span className="text-xs text-gray-400">
                    {translation.text.length}/500 caratteri
                  </span>
                </label>
                <textarea
                  value={translation.text}
                  onChange={(e) => handleTranslationChange(translation.lang, e.target.value)}
                  className={`w-full p-2 border rounded bg-[#273040] text-white ${
                    translation.text.trim() === '' ? 'border-red-500' : 'border-gray-600'
                  }`}
                  rows="3"
                  maxLength="500"
                  required
                  placeholder={`Inserisci il testo in ${translation.lang === 'it' ? 'italiano' : 'inglese'}...`}
                ></textarea>
                {translation.text.trim() === '' && (
                  <p className="text-red-500 text-xs mt-1">
                    Il testo in {translation.lang === 'it' ? 'italiano' : 'inglese'} è obbligatorio
                  </p>
                )}
              </div>
            ))}
          </div>
            <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded mr-2"
            >
              Annulla
            </button>
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
            >
              Salva
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Componente modal per la conferma dell'eliminazione
function ConfirmDeleteModal({ onClose, onConfirm }) {
  return (
    <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-[#1e252e] rounded-lg p-6 max-w-md w-full">
        <h2 className="text-xl font-bold mb-4 text-white">Conferma eliminazione</h2>
        <p className="text-gray-300 mb-6">
          Sei sicuro di voler eliminare questa sfida? L'azione non è reversibile e verranno eliminate anche tutte le traduzioni associate.
        </p>
        
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded mr-2"
          >
            Annulla
          </button>
          <button
            onClick={onConfirm}
            className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded"
          >
            Elimina
          </button>
        </div>
      </div>
    </div>
  );
}
