'use client';

import { useState, useEffect } from 'react';
import supabase from '@/lib/supabase.js';
import { useRouter } from 'next/navigation';

export default function DifficoltaPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [filteredLevels, setFilteredLevels] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [editMode, setEditMode] = useState(false);
  const [showForm, setShowForm] = useState(false);
  
  // Opzioni per team_difficulty_hook
  const difficultyOptions = [
    { value: 'very_easy', label: 'Molto facile' },
    { value: 'easy', label: 'Facile' },
    { value: 'medium', label: 'Media' },
    { value: 'hard', label: 'Difficile' },
    { value: 'very_hard', label: 'Molto difficile' },
    { value: 'crazy', label: 'Matta' },
    { value: 'meme', label: 'Meme' },
  ];
  
  // Stati per il form
  const [formData, setFormData] = useState({
    id: null,
    name: '',
    lang: '',
    difficulty_challenge_text: '',
    difficulty_challenge_hook: 'medium' // valore predefinito
  });
  
  // Stato per i filtri
  const [selectedLang, setSelectedLang] = useState('');
  const [availableLanguages, setAvailableLanguages] = useState([]);
  
  // Caricamento iniziale
  useEffect(() => {
    // Verifica se l'utente è autenticato e carica i livelli di difficoltà
    const checkSessionAndLoadData = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        router.push('/login');
        return;
      }
      
      await loadDifficultyLevels();
    };
    
    checkSessionAndLoadData();
  }, [router]);
  
  // Effetto per filtrare in base alla lingua selezionata
  useEffect(() => {
    if (difficultyLevels.length > 0) {
      if (selectedLang) {
        setFilteredLevels(difficultyLevels.filter(level => level.lang === selectedLang));
      } else {
        setFilteredLevels(difficultyLevels);
      }
      
      // Estrai lingue uniche per il filtro
      const languages = [...new Set(difficultyLevels.map(level => level.lang))].filter(Boolean).sort();
      setAvailableLanguages(languages);
    }
  }, [difficultyLevels, selectedLang]);
  
  // Caricamento dei dati dal database
  const loadDifficultyLevels = async () => {
    try {
      setLoading(true);
      
      // Carica i livelli di difficoltà dal database
      const { data, error } = await supabase
        .from('difficulty_challenges')
        .select('*')
        .order('id');
      
      if (error) {
        console.error('Errore nel caricamento dei livelli di difficoltà:', error.message);
        throw error;
      }
      
      setDifficultyLevels(data || []);
      setFilteredLevels(data || []);
      
      // Estrai lingue uniche per il filtro
      if (data && data.length > 0) {
        const languages = [...new Set(data.map(level => level.lang))].filter(Boolean).sort();
        setAvailableLanguages(languages);
      }
      
    } catch (error) {
      console.error('Errore:', error.message || JSON.stringify(error));
    } finally {
      setLoading(false);
    }
  };
  
  // Gestione del form di creazione/modifica
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Salvataggio del form (creazione o aggiornamento)
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (editMode) {
        // Aggiorna un livello esistente
        const { error } = await supabase
          .from('difficulty_challenges')
          .update({
            name: formData.name,
            lang: formData.lang,
            difficulty_challenge_text: formData.difficulty_challenge_text,
            difficulty_challenge_hook: formData.difficulty_challenge_hook
          })
          .eq('id', formData.id);
        
        if (error) throw error;
        
      } else {
        // Crea un nuovo livello
        const { error } = await supabase
          .from('difficulty_challenges')
          .insert([{
            name: formData.name,
            lang: formData.lang,
            difficulty_challenge_text: formData.difficulty_challenge_text,
            difficulty_challenge_hook: formData.difficulty_challenge_hook
          }]);
        
        if (error) throw error;
      }
      
      // Resetta il form e ricarica i dati
      resetForm();
      await loadDifficultyLevels();
      
    } catch (error) {
      console.error('Errore durante il salvataggio:', error.message);
      alert(`Si è verificato un errore: ${error.message}`);
    }
  };
  
  // Modifica di un livello esistente
  const handleEdit = (level) => {
    setFormData({
      id: level.id,
      name: level.name || '',
      lang: level.lang || '',
      difficulty_challenge_text: level.difficulty_challenge_text || '',
      difficulty_challenge_hook: level.difficulty_challenge_hook || 'medium'
    });
    setEditMode(true);
    setShowForm(true);
    // Scrollare al form
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Eliminazione di un livello
  const handleDelete = async (id) => {
    if (window.confirm('Sei sicuro di voler eliminare questo livello di difficoltà?')) {
      try {
        const { error } = await supabase
          .from('difficulty_challenges')
          .delete()
          .eq('id', id);
        
        if (error) throw error;
        
        await loadDifficultyLevels();
        
      } catch (error) {
        console.error('Errore durante l\'eliminazione:', error.message);
        alert(`Si è verificato un errore: ${error.message}`);
      }
    }
  };
  
  // Reset del form
  const resetForm = () => {
    setFormData({
      id: null,
      name: '',
      lang: '',
      difficulty_challenge_text: '',
      difficulty_challenge_hook: 'medium'
    });
    setEditMode(false);
    setShowForm(false);
  };
  
  // Gestione per mostrare il form di creazione
  const handleShowCreateForm = () => {
    setEditMode(false);
    setFormData({
      id: null,
      name: '',
      lang: '',
      difficulty_challenge_text: '',
      difficulty_challenge_hook: 'medium'
    });
    setShowForm(true);
    // Scrollare al form
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Calcolo per la paginazione
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredLevels.slice(indexOfFirstItem, indexOfLastItem);
  
  // Cambio pagina
  const paginate = (pageNumber) => setCurrentPage(pageNumber);
  
  // Spinner di caricamento
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestione Livelli di Difficoltà</h1>
        {!showForm && (
          <button 
            onClick={handleShowCreateForm}
            className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md"
          >
            Aggiungi Nuovo Livello
          </button>
        )}
      </div>
      
      {/* Sezione Form per creazione/modifica - visibile solo quando necessario */}
      {showForm && (
        <div className="bg-[#2d3748] rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">{editMode ? 'Modifica Livello di Difficoltà' : 'Crea Nuovo Livello di Difficoltà'}</h2>
          
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">Nome</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="w-full p-2 bg-[#1e252e] border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Lingua</label>
                <input
                  type="text"
                  name="lang"
                  value={formData.lang}
                  onChange={handleInputChange}
                  required
                  className="w-full p-2 bg-[#1e252e] border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="es: it, en, fr..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Livello di Difficoltà</label>
                <select
                  name="difficulty_challenge_hook"
                  value={formData.difficulty_challenge_hook}
                  onChange={handleInputChange}
                  required
                  className="w-full p-2 bg-[#1e252e] border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {difficultyOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-sm font-medium mb-2">Testo Descrittivo</label>
                <textarea
                  name="difficulty_challenge_text"
                  value={formData.difficulty_challenge_text}
                  onChange={handleInputChange}
                  required
                  rows={4}
                  className="w-full p-2 bg-[#1e252e] border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                ></textarea>
              </div>
            </div>
            
            <div className="flex space-x-4 mt-6">
              <button
                type="submit"
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md"
              >
                {editMode ? 'Aggiorna' : 'Crea'}
              </button>
              
              <button
                type="button"
                onClick={resetForm}
                className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md"
              >
                Annulla
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Sezione Tabella con filtri */}
      <div className="bg-[#2d3748] rounded-lg shadow-md overflow-hidden">
        <div className="p-4 border-b border-gray-700 flex items-center justify-between flex-wrap gap-4">
          <h3 className="text-lg font-medium">Livelli di Difficoltà</h3>
          
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium">Filtra per lingua:</label>
            <select
              value={selectedLang}
              onChange={(e) => setSelectedLang(e.target.value)}
              className="bg-[#1e252e] border border-gray-700 rounded-md p-1"
            >
              <option value="">Tutte</option>
              {availableLanguages.map(lang => (
                <option key={lang} value={lang}>{lang}</option>
              ))}
            </select>
          </div>
        </div>
        
        {filteredLevels.length > 0 ? (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-[#1e252e]">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Nome
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Lingua
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Livello
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                      Descrizione
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      Azioni
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {currentItems.map((level) => (
                    <tr key={level.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium">{level.name}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">{level.lang}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm">
                          {difficultyOptions.find(opt => opt.value === level.difficulty_challenge_hook)?.label || level.difficulty_challenge_hook}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm max-w-md truncate">
                          {level.difficulty_challenge_text}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                        <button
                          onClick={() => handleEdit(level)}
                          className="text-blue-400 hover:text-blue-300 mr-3"
                        >
                          Modifica
                        </button>
                        <button
                          onClick={() => handleDelete(level.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          Elimina
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {/* Paginazione */}
            {filteredLevels.length > itemsPerPage && (
              <div className="px-6 py-3 flex justify-center">
                <nav className="flex items-center">
                  {Array.from({ length: Math.ceil(filteredLevels.length / itemsPerPage) }).map((_, index) => (
                    <button
                      key={index}
                      onClick={() => paginate(index + 1)}
                      className={`px-3 py-1 mx-1 rounded-md ${
                        currentPage === index + 1 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-[#1e252e] hover:bg-gray-700'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                </nav>
              </div>
            )}
          </>
        ) : (
          <div className="p-6 text-center">
            <p>Nessun livello di difficoltà trovato.</p>
            {selectedLang && (
              <p className="mt-2">
                Prova a rimuovere il filtro per lingua o crea un nuovo livello di difficoltà.
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
