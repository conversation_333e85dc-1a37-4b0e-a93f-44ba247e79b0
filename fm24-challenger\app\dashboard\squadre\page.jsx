'use client';

import { useState, useEffect } from 'react';
import supabase from '@/lib/supabase.js';
import { useRouter } from 'next/navigation';

export default function SquadrePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [squadre, setSquadre] = useState([]);
  
  useEffect(() => {
    // Verifica se l'utente è autenticato e carica le squadre
    const checkSessionAndLoadSquadre = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        router.push('/login');
        return;
      }
      
      // Qui potresti caricare le squadre dal database
      // Per ora utilizziamo dei dati di esempio
      setSquadre([
        { id: 1, nome: 'AC Milan', campionato: 'Serie A', rating: 4.5 },
        { id: 2, nome: 'FC Barcelona', campionato: 'La Liga', rating: 4.7 },
        { id: 3, nome: 'Manchester United', campionato: 'Premier League', rating: 4.3 },
      ]);
      
      setLoading(false);
    };
    
    checkSessionAndLoadSquadre();
  }, [router]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Gestione Squadre</h1>
        <button className="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">
          Aggiungi Squadra
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nome
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Campionato
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rating
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {squadre.map((squadra) => (
              <tr key={squadra.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="font-medium text-gray-900">{squadra.nome}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-500">{squadra.campionato}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-500">{squadra.rating}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button className="text-blue-600 hover:text-blue-900 mr-3">
                    Modifica
                  </button>
                  <button className="text-red-600 hover:text-red-900">
                    Elimina
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
