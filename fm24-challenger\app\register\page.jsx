'use client';

import { useState } from 'react';
import supabase from '@/lib/supabase.js';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

export default function RegisterPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [loading, setLoading] = useState(false);
  
  const handleRegister = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg('');

    // Validazione dell'email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setErrorMsg('Formato email non valido');
      setLoading(false);
      return;
    }

    // Validazione della password
    if (password.length < 6) {
      setErrorMsg('La password deve essere di almeno 6 caratteri');
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        console.error('Errore di registrazione:', error);
        if (error.message.includes('email')) {
          setErrorMsg('Email non valida o già utilizzata');
        } else if (error.message.includes('password')) {
          setErrorMsg('La password non è accettabile');
        } else {
          setErrorMsg(error.message);
        }
        setLoading(false);
        return;
      }      // Inserimento nella tabella `users` con stesso ID utente
      const userId = data.user?.id;

      if (userId) {
        // Includiamo l'ID e impostiamo l'opzione per bypassare RLS
        const { error: insertError } = await supabase.from('users').insert([
          {
            id: userId, // Includiamo esplicitamente l'ID utente
            email,
            username,
          },
        ]).select();

        if (insertError) {
          console.error('Errore inserimento utente:', insertError);
          setErrorMsg('Registrazione riuscita ma errore nel salvataggio dei dati utente.');
          setLoading(false);
          return;
        }
      }

      router.push('/dashboard'); // Redirect post-registrazione
    } catch (error) {
      console.error('Errore imprevisto:', error);
      setErrorMsg('Si è verificato un errore durante la registrazione');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Registrati</h1>
      <form onSubmit={handleRegister}>
        <input
          type="text"
          placeholder="Username"
          className="w-full p-2 mb-2 border"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
        />
        <input
          type="email"
          placeholder="Email"
          className="w-full p-2 mb-2 border"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        <input
          type="password"
          placeholder="Password"
          className="w-full p-2 mb-2 border"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />        {errorMsg && <p className="text-red-500">{errorMsg}</p>}        <button
            className="bg-green-600 text-white px-4 py-2 mt-2 w-full" 
            disabled={loading}
            >
            {loading ? 'Registrazione...' : 'Registrati'}
            </button>
      </form>
      <p className="mt-4 text-center">
        Hai già un account?{' '}
        <Link href="/login" className="text-blue-600 hover:underline">
          Accedi
        </Link>
      </p>
    </div>
  );
}
