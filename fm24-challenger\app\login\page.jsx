'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import supabase from '@/lib/supabase.js';

export default function LoginPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorMsg, setErrorMsg] = useState('');
  const [loading, setLoading] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg('');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Errore di login:', error);
        setErrorMsg('Email o password non validi');
        setLoading(false);
        return;
      }

      // Otteniamo i dati dell'utente
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('username')
        .eq('email', email)
        .single();

      if (userError) {
        console.error('Errore nel recupero dei dati utente:', userError);
      }

      // Redirect alla dashboard
      router.push('/dashboard');
    } catch (error) {
      console.error('Errore imprevisto:', error);
      setErrorMsg('Si è verificato un errore durante il login');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Accedi</h1>
      <form onSubmit={handleLogin}>
        <input
          type="email"
          placeholder="Email"
          className="w-full p-2 mb-2 border"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
        <input
          type="password"
          placeholder="Password"
          className="w-full p-2 mb-2 border"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
        {errorMsg && <p className="text-red-500">{errorMsg}</p>}
        <button
          className="bg-blue-600 text-white px-4 py-2 mt-2 w-full"
          disabled={loading}
        >
          {loading ? 'Accesso in corso...' : 'Accedi'}
        </button>
      </form>
      <p className="mt-4 text-center">
        Non hai un account?{' '}
        <Link href="/register" className="text-blue-600 hover:underline">
          Registrati
        </Link>
      </p>
    </div>
  );
}
