'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Sidebar() {
  const [activeItem, setActiveItem] = useState('squadre');
  return (
    <div className="bg-[#1e252e] text-white w-64 min-h-screen p-4 border-r border-gray-700">
      <h2 className="text-xl font-bold mb-6 text-center text-blue-400">FM24 Challenger</h2>
      
      <nav>
        <ul>
          <li className="mb-2">
            <Link 
              href="/dashboard/squadre"
              className={`block p-3 rounded ${activeItem === 'squadre' ? 'bg-blue-600' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveItem('squadre')}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Gestisci Squadre
              </div>
            </Link>
          </li>
          <li className="mb-2">
            <Link 
              href="/dashboard/sfide"
              className={`block p-3 rounded ${activeItem === 'sfide' ? 'bg-blue-600' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveItem('sfide')}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Gestisci Sfide
              </div>
            </Link>
          </li>
          <li>
            <Link 
              href="/dashboard/difficolta"
              className={`block p-3 rounded ${activeItem === 'difficolta' ? 'bg-blue-600' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveItem('difficolta')}
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Gestione Difficoltà
              </div>
            </Link>
          </li>
          <li>
            <Link 
              href="/dashboard/difficolta_sfide"
              className={`block p-3 rounded ${activeItem === 'difficolta_sfide' ? 'bg-blue-600' : 'hover:bg-gray-700'}`}
              onClick={() => setActiveItem('difficolta_sfide')} 
            >
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Gestione Difficoltà Sfide 
              </div>
            </Link>
          </li>
        </ul>

      </nav>
    </div>
  );
}
