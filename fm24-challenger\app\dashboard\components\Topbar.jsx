'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import supabase from '@/lib/supabase.js';

export default function Topbar() {
  const router = useRouter();
  const [username, setUsername] = useState('');
  
  useEffect(() => {
    // Ottieni i dati dell'utente corrente
    const fetchUserData = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data } = await supabase
          .from('users')
          .select('username')
          .eq('id', user.id)
          .single();
          
        if (data) {
          setUsername(data.username);
        }
      } else {
        // Se non c'è un utente autenticato, reindirizza al login
        router.push('/login');
      }
    };
    
    fetchUserData();
  }, [router]);

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    
    if (!error) {
      router.push('/login');
    }
  };
  return (
    <div className="bg-[#273040] border-b border-gray-700 p-4 flex justify-between items-center">
      <div className="text-xl font-semibold text-white">Dashboard</div>
      
      <div className="flex items-center">
        <div className="mr-4">
          <span className="text-gray-300">Benvenuto, </span>
          <span className="font-semibold text-blue-400">{username}</span>
        </div>
        
        <button 
          onClick={handleLogout}
          className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 rounded-md flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          Logout
        </button>
      </div>
    </div>
  );
}
