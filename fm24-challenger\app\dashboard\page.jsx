'use client';

import { useEffect, useState } from 'react';
import supabase from '@/lib/supabase.js';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Verifica se l'utente è autenticato
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        router.push('/login');
      } else {
        setLoading(false);
      }
    };
    
    checkSession();
  }, [router]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  return (
    <div>
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Le tue squadre</h2>
          <p>Gestisci le tue squadre qui. Clicca su "Gestisci Squadre" nel menu laterale.</p>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Le tue sfide</h2>
          <p>Gestisci le tue sfide qui. Clicca su "Gestisci Sfide" nel menu laterale.</p>
        </div>
      </div>
      
      <div className="mt-6 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold mb-4">Attività recenti</h2>
        <p className="text-gray-500">Nessuna attività recente da mostrare.</p>
      </div>
    </div>
  );
}
